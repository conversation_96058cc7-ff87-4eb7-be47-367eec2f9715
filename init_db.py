#!/usr/bin/env python3
"""
数据库检查和示例数据插入脚本
"""

from app import app
from models import db, VehicleModel, ComponentModel, TestProjectModel, ModalDataModel
from datetime import date

def check_and_insert_data():
    """检查数据库并插入示例数据"""

    with app.app_context():
        # 检查是否已有数据
        vehicle_count = VehicleModel.query.count()
        if vehicle_count > 0:
            print(f"数据库中已有 {vehicle_count} 个车型数据，跳过数据插入")
            return

        # 插入示例数据
        print("插入示例数据...")
        insert_sample_data()

        print("示例数据插入完成！")

def insert_sample_data():
    """插入示例数据"""
    
    # 插入车型数据
    vehicles = [
        VehicleModel(
            vehicle_model_code='SGM001',
            vehicle_model_name='宝骏530',
            vin='LSGJA52U0JG123456',
            drive_type='FWD',
            configuration='1.5T CVT豪华型',
            production_year=2023
        ),
        VehicleModel(
            vehicle_model_code='SGM002',
            vehicle_model_name='宝骏510',
            vin='LSGJA52U0JG123457',
            drive_type='FWD',
            configuration='1.5L MT舒适型',
            production_year=2023
        ),
        VehicleModel(
            vehicle_model_code='SGM003',
            vehicle_model_name='五菱宏光MINI EV',
            vin='LSGJA52U0JG123458',
            drive_type='RWD',
            configuration='磷酸铁锂版',
            production_year=2023
        )
    ]
    
    for vehicle in vehicles:
        db.session.add(vehicle)
    
    # 插入零部件数据
    components = [
        ComponentModel(
            component_code='SUSP001',
            component_name='前悬架总成',
            category='底盘系统',
            sub_category='悬架'
        ),
        ComponentModel(
            component_code='SUSP002',
            component_name='后悬架总成',
            category='底盘系统',
            sub_category='悬架'
        ),
        ComponentModel(
            component_code='ENG001',
            component_name='发动机总成',
            category='底盘系统',
            sub_category='动力总成'
        ),
        ComponentModel(
            component_code='BODY001',
            component_name='车身骨架',
            category='车身系统',
            sub_category='车身'
        ),
        ComponentModel(
            component_code='DOOR001',
            component_name='前车门',
            category='车身系统',
            sub_category='开闭件'
        )
    ]
    
    for component in components:
        db.session.add(component)
    
    # 提交以获取ID
    db.session.commit()
    
    # 插入测试项目数据
    projects = [
        TestProjectModel(
            project_code='TEST001',
            project_name='宝骏530整车模态测试',
            vehicle_model_id=1,
            component_id=None,
            test_type='模态测试',
            test_date=date(2023, 12, 1),
            test_engineer='张工',
            test_condition='整车状态',
            test_status='整车状态',
            excitation_method='锤击法'
        ),
        TestProjectModel(
            project_code='TEST002',
            project_name='宝骏530前悬架模态测试',
            vehicle_model_id=1,
            component_id=1,
            test_type='模态测试',
            test_date=date(2023, 12, 2),
            test_engineer='李工',
            test_condition='自由状态',
            test_status='零件自由状态',
            excitation_method='激振器'
        ),
        TestProjectModel(
            project_code='TEST003',
            project_name='宝骏510车身模态测试',
            vehicle_model_id=2,
            component_id=4,
            test_type='模态测试',
            test_date=date(2023, 12, 3),
            test_engineer='王工',
            test_condition='约束状态',
            test_status='零件约束状态',
            excitation_method='锤击法'
        )
    ]
    
    for project in projects:
        db.session.add(project)
    
    # 提交以获取ID
    db.session.commit()
    
    # 插入模态数据
    modal_data = [
        ModalDataModel(
            test_project_id=1,
            mode_order=1,
            direction='Z',
            frequency=12.5,
            damping_ratio=0.05,
            mode_shape_description='车身垂直弯曲',
            notes='第一阶整车模态',
            updated_by='admin'
        ),
        ModalDataModel(
            test_project_id=1,
            mode_order=2,
            direction='Y',
            frequency=15.8,
            damping_ratio=0.04,
            mode_shape_description='车身侧向弯曲',
            notes='第二阶整车模态',
            updated_by='admin'
        ),
        ModalDataModel(
            test_project_id=2,
            mode_order=1,
            direction='Z',
            frequency=25.3,
            damping_ratio=0.08,
            mode_shape_description='悬架垂直振动',
            notes='悬架第一阶模态',
            updated_by='admin'
        ),
        ModalDataModel(
            test_project_id=3,
            mode_order=1,
            direction='X',
            frequency=18.2,
            damping_ratio=0.06,
            mode_shape_description='车身扭转',
            notes='车身第一阶扭转模态',
            updated_by='admin'
        ),
        ModalDataModel(
            test_project_id=3,
            mode_order=2,
            direction='Z',
            frequency=22.7,
            damping_ratio=0.05,
            mode_shape_description='车身弯曲',
            notes='车身第一阶弯曲模态',
            updated_by='admin'
        )
    ]
    
    for modal in modal_data:
        db.session.add(modal)
    
    # 最终提交
    db.session.commit()
    
    print(f"插入了 {len(vehicles)} 个车型")
    print(f"插入了 {len(components)} 个零部件")
    print(f"插入了 {len(projects)} 个测试项目")
    print(f"插入了 {len(modal_data)} 条模态数据")

if __name__ == '__main__':
    check_and_insert_data()
