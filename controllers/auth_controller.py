from flask import Blueprint, request, session, redirect, url_for, render_template
from utils.result import success, unauthorized

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

class AuthController:
    """认证控制器"""

    @staticmethod
    @auth_bp.route('/logout', methods=['POST'])
    def logout():
        """登出"""
        session.clear()
        return success(message="登出成功")

    @staticmethod
    @auth_bp.route('/user/info')
    def get_user_info():
        """获取用户信息"""
        user_info = session.get('user_info')
        if not user_info:
            return unauthorized("用户未登录")
        return success(user_info)
