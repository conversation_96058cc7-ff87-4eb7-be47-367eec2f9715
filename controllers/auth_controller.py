from flask import Blueprint, request, session, redirect, url_for, render_template, current_app
from authlib.integrations.flask_client import OAuth
from utils.result import success, error, unauthorized

auth_bp = Blueprint('auth', __name__, url_prefix='/auth')

class AuthController:
    """认证控制器"""
    
    @staticmethod
    @auth_bp.route('/login')
    def login():
        """登录页面"""
        # 如果已经登录，重定向到首页
        if 'user_info' in session:
            return redirect(url_for('index'))
        return render_template('login.html')
    
    @staticmethod
    @auth_bp.route('/oauth/login')
    def oauth_login():
        """OAuth登录"""
        oauth = current_app.extensions.get('authlib.integrations.flask_client')
        keycloak = oauth.keycloak
        
        # 生成授权URL并重定向
        redirect_uri = url_for('auth.oauth_callback', _external=True)
        return keycloak.authorize_redirect(redirect_uri)
    
    @staticmethod
    @auth_bp.route('/oauth/callback')
    def oauth_callback():
        """OAuth回调处理"""
        oauth = current_app.extensions.get('authlib.integrations.flask_client')
        keycloak = oauth.keycloak
        
        try:
            # 获取访问令牌
            token = keycloak.authorize_access_token()
            
            # 获取用户信息
            user_info = keycloak.parse_id_token(token)
            
            # 存储用户信息到session
            session['user_info'] = {
                'id': user_info.get('sub'),
                'username': user_info.get('preferred_username'),
                'email': user_info.get('email'),
                'name': user_info.get('name'),
                'roles': user_info.get('realm_access', {}).get('roles', [])
            }
            session['access_token'] = token.get('access_token')
            
            return redirect(url_for('index'))
            
        except Exception as e:
            current_app.logger.error(f"OAuth callback error: {str(e)}")
            return redirect(url_for('auth.login', error='登录失败'))
    
    @staticmethod
    @auth_bp.route('/logout', methods=['POST'])
    def logout():
        """登出"""
        session.clear()
        return success(message="登出成功")
    
    @staticmethod
    @auth_bp.route('/user/info')
    def get_user_info():
        """获取用户信息"""
        user_info = session.get('user_info')
        if not user_info:
            return unauthorized("用户未登录")
        return success(user_info)
