/**
 * 基础JavaScript功能
 */

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * 初始化应用
 */
function initializeApp() {
    // 初始化侧边栏
    initializeSidebar();
    
    // 初始化用户信息
    initializeUserInfo();
    
    // 设置当前页面的导航高亮
    highlightCurrentNav();
}

/**
 * 初始化侧边栏
 */
function initializeSidebar() {
    // 处理折叠菜单
    const collapseElements = document.querySelectorAll('[data-bs-toggle="collapse"]');
    collapseElements.forEach(element => {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('data-bs-target'));
            if (target) {
                const collapse = new bootstrap.Collapse(target, {
                    toggle: true
                });
            }
        });
    });
}

/**
 * 初始化用户信息
 */
function initializeUserInfo() {
    // 可以在这里加载用户信息
    console.log('用户信息初始化完成');
}

/**
 * 高亮当前导航
 */
function highlightCurrentNav() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href && currentPath.startsWith(href) && href !== '/') {
            link.classList.add('active');
            
            // 展开父级折叠菜单
            const parentCollapse = link.closest('.collapse');
            if (parentCollapse) {
                parentCollapse.classList.add('show');
            }
        }
    });
}

/**
 * 登出功能
 */
async function logout() {
    try {
        const result = await request.post('/auth/logout');
        showMessage('登出成功', 'success');
        setTimeout(() => {
            window.location.href = '/auth/login';
        }, 1000);
    } catch (error) {
        showMessage('登出失败: ' + error.message, 'error');
    }
}

/**
 * 显示"敬请期待"消息
 */
function showComingSoon() {
    showMessage('该功能正在开发中，敬请期待！', 'info');
}

/**
 * 格式化日期
 */
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN');
}

/**
 * 格式化数字
 */
function formatNumber(number, decimals = 2) {
    if (number === null || number === undefined) return '-';
    return Number(number).toFixed(decimals);
}

/**
 * 防抖函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * 节流函数
 */
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

/**
 * 显示加载状态
 */
function showLoading(element, text = '加载中...') {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = `
            <div class="text-center py-4">
                <div class="loading-spinner mb-2"></div>
                <div class="text-muted">${text}</div>
            </div>
        `;
    }
}

/**
 * 隐藏加载状态
 */
function hideLoading(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    
    if (element) {
        element.innerHTML = '';
    }
}

/**
 * 确认对话框
 */
function confirmDialog(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

/**
 * 复制到剪贴板
 */
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        showMessage('已复制到剪贴板', 'success');
    } catch (error) {
        showMessage('复制失败', 'error');
    }
}

/**
 * 下载文件
 */
function downloadFile(url, filename) {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

/**
 * 获取URL参数
 */
function getUrlParameter(name) {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get(name);
}

/**
 * 设置URL参数
 */
function setUrlParameter(name, value) {
    const url = new URL(window.location);
    url.searchParams.set(name, value);
    window.history.pushState({}, '', url);
}
