/* 模态搜索页面样式 */

/* 搜索表单样式 */
.search-form {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
}

/* 搜索结果表格样式 */
.search-results {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
}

.table-responsive {
    border-radius: 0.5rem;
}

.table th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.table td {
    vertical-align: middle;
    text-align: center;
    border-color: #e9ecef;
}

/* 频率数值高亮 */
.frequency-value {
    font-weight: 600;
    color: #007bff;
}

/* 操作按钮样式 */
.btn-action {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 模态详情弹窗样式 */
.modal-detail-content {
    max-height: 70vh;
    overflow-y: auto;
}

.detail-section {
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border-left: 4px solid #007bff;
}

.detail-section h6 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 0.75rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e9ecef;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 500;
    color: #6c757d;
    min-width: 120px;
}

.detail-value {
    color: #495057;
    text-align: right;
}

/* 图片预览样式 */
.image-preview {
    max-width: 100%;
    max-height: 300px;
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 加载状态样式 */
.loading-state {
    text-align: center;
    padding: 2rem;
}

.loading-spinner {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    border: 0.25rem solid #f3f3f3;
    border-top: 0.25rem solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 搜索条件标签 */
.search-tags {
    margin-bottom: 1rem;
}

.search-tag {
    display: inline-block;
    background-color: #e9ecef;
    color: #495057;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.search-tag .remove-tag {
    margin-left: 0.5rem;
    cursor: pointer;
    color: #6c757d;
}

.search-tag .remove-tag:hover {
    color: #dc3545;
}

/* 结果统计 */
.result-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 600;
    display: block;
}

.stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .search-form {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn-action {
        padding: 0.125rem 0.5rem;
        font-size: 0.75rem;
    }
}
