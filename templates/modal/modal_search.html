{% extends "base.html" %}

{% block title %}模态数据查询 - NVH数据管理系统{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='css/modal_search.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">模态数据查询</h1>
</div>

<!-- 搜索表单 -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>搜索条件
        </h5>
    </div>
    <div class="card-body">
        <form id="search-form" class="row g-3">
            <div class="col-md-4">
                <label for="vehicle-select" class="form-label">车型选择</label>
                <select class="form-select" id="vehicle-select" required>
                    <option value="">请选择车型</option>
                </select>
            </div>
            <div class="col-md-4">
                <label for="component-select" class="form-label">零件选择</label>
                <select class="form-select" id="component-select">
                    <option value="">全部零件</option>
                </select>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button type="button" id="search-btn" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>搜索
                </button>
                <button type="button" id="export-btn" class="btn btn-outline-success" disabled>
                    <i class="fas fa-download me-1"></i>导出数据
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 搜索结果 -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="fas fa-table me-2"></i>搜索结果
        </h5>
        <span id="result-count" class="badge bg-secondary">0 条记录</span>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>分类</th>
                        <th>子分类</th>
                        <th>零件名称</th>
                        <th>频率 (Hz)</th>
                        <th>阶次</th>
                        <th>模态类型</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="table-body">
                    <tr>
                        <td colspan="7" class="text-center text-muted py-4">
                            <i class="fas fa-search fa-2x mb-2"></i><br>
                            请选择搜索条件并点击搜索按钮
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 模态详情弹窗 -->
<div class="modal fade" id="modal-detail-modal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">模态详情查看</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modal-detail-content">
                    <!-- 详情内容将通过JS动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/modal_search.js') }}"></script>
{% endblock %}
