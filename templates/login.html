{% extends "base.html" %}

{% block title %}登录 - NVH数据管理系统{% endblock %}

{% block content %}
<div class="row justify-content-center align-items-center min-vh-100">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-body p-5">
                <div class="text-center mb-4">
                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                    <h3>NVH数据管理系统</h3>
                    <p class="text-muted">请使用您的账号登录</p>
                </div>
                
                {% if request.args.get('error') %}
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    {{ request.args.get('error') }}
                </div>
                {% endif %}
                
                <div class="d-grid">
                    <a href="{{ url_for('auth.oauth_login') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        使用Keycloak登录
                    </a>
                </div>
                
                <div class="text-center mt-4">
                    <small class="text-muted">
                        使用您的企业账号登录系统
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.min-vh-100 {
    min-height: 100vh;
}
</style>
{% endblock %}
