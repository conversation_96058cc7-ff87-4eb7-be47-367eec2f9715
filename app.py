import os
from flask import Flask, render_template, session
from authlib.integrations.flask_client import <PERSON>A<PERSON>
from config import config
from models.base_model import db
from controllers.auth_controller import auth_bp
from controllers.modal_controller import modal_bp
from decorators import login_required

def create_app(config_name=None):
    """应用工厂函数"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # 初始化数据库
    db.init_app(app)
    
    # 初始化OAuth
    oauth = OAuth(app)
    
    # 注册Keycloak客户端
    keycloak = oauth.register(
        name='keycloak',
        client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
        client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
        server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
        client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
    )
    
    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(modal_bp)
    
    # 首页路由
    @app.route('/')
    def index():
        """首页"""
        return render_template('index.html')
    
    # 错误处理
    @app.errorhandler(404)
    def not_found(error):
        return render_template('404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return render_template('500.html'), 500
    
    # 创建数据库表
    with app.app_context():
        try:
            db.create_all()
            print("数据库表创建成功")
        except Exception as e:
            print(f"数据库表创建失败: {e}")
    
    # 创建上传目录
    upload_folder = app.config.get('UPLOAD_FOLDER')
    if upload_folder and not os.path.exists(upload_folder):
        os.makedirs(upload_folder, exist_ok=True)
        os.makedirs(os.path.join(upload_folder, 'modal_shapes'), exist_ok=True)
        os.makedirs(os.path.join(upload_folder, 'test_photos'), exist_ok=True)
    
    return app

# 创建应用实例
app = create_app()

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
