# NVH数据管理系统 - 快速启动指南

## 方式一：本地开发环境

### 1. 环境准备
- Python 3.8+
- MySQL 8.0+
- Git

### 2. 克隆项目
```bash
git clone <repository-url>
cd nvh_aug
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 配置数据库
1. 启动MySQL服务
2. 创建数据库：
```sql
CREATE DATABASE nvh_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 5. 配置环境变量
```bash
cp .env.example .env
```
编辑 `.env` 文件，修改数据库连接信息。

### 6. 初始化数据库
```bash
python init_db.py
```

### 7. 启动应用
```bash
python run.py
```

访问 http://localhost:5000

## 方式二：Docker部署

### 1. 安装Docker和Docker Compose

### 2. 启动服务
```bash
docker-compose up -d
```

### 3. 初始化数据库
```bash
docker-compose exec nvh_app python init_db.py
```

访问 http://localhost:5000

## 默认测试数据

系统初始化后包含以下测试数据：

### 车型
- 宝骏530 (SGM001)
- 宝骏510 (SGM002)  
- 五菱宏光MINI EV (SGM003)

### 零部件
- 前悬架总成
- 后悬架总成
- 发动机总成
- 车身骨架
- 前车门

### 模态数据
- 5条模态测试数据，包含不同车型和零部件的模态信息

## 功能测试

### 1. 登录系统
- 访问首页，点击"立即登录"
- 使用Keycloak账号登录（需要配置Keycloak服务器）

### 2. 模态数据查询
- 登录后，点击左侧菜单"NVH数据查询" -> "模态数据"
- 选择车型：宝骏530
- 点击"搜索"按钮
- 查看搜索结果，点击"查看详情"

### 3. 数据导出
- 在搜索结果页面点击"导出数据"
- 下载CSV文件

## 开发说明

### 项目结构
```
nvh_system/
├── app.py              # 应用入口
├── config.py           # 配置文件
├── models/             # 数据模型
├── controllers/        # 控制器
├── services/          # 业务逻辑
├── templates/         # 模板文件
├── static/           # 静态文件
└── utils/            # 工具类
```

### 添加新功能
1. 在 `models/` 创建数据模型
2. 在 `services/` 创建业务逻辑
3. 在 `controllers/` 创建控制器
4. 在 `templates/` 创建页面模板
5. 在 `static/` 创建样式和脚本

### API接口
- 所有API接口返回统一的JSON格式
- 使用装饰器进行认证和权限控制
- 错误处理统一在前端处理

## 故障排除

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 检查 `.env` 文件中的数据库配置
- 确认数据库用户权限

### 2. Keycloak认证失败
- 检查Keycloak服务器是否可访问
- 确认客户端ID和密钥配置正确
- 检查回调URL配置

### 3. 静态文件加载失败
- 确认Flask应用正确启动
- 检查静态文件路径配置
- 清除浏览器缓存

### 4. 模态数据查询无结果
- 确认数据库已正确初始化
- 检查测试数据是否插入成功
- 查看应用日志排查错误

## 联系支持

如有问题，请查看：
1. README.md 详细文档
2. 应用日志文件
3. 数据库连接状态
4. Keycloak服务状态
