#!/usr/bin/env python3
"""
测试应用 - 简化版本用于调试
"""

from flask import Flask, render_template, session, redirect, url_for
from authlib.integrations.flask_client import OAuth

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'nvh-system-secret-key-2024'

# Keycloak配置
app.config['KEYCLOAK_FRONTEND_CLIENT_ID'] = 'front'
app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'] = 'frontend-secret'
app.config['KEYCLOAK_SERVER_METADATA_URL'] = 'https://account-test.sgmw.com.cn/auth/realms/demo/.well-known/openid-configuration'
app.config['KEYCLOAK_CLIENT_KWARGS'] = {
    'scope': 'openid email profile'
}

# 初始化OAuth
oauth = OAuth(app)

# 注册Keycloak客户端
keycloak = oauth.register(
    name='keycloak',
    client_id=app.config['KEYCLOAK_FRONTEND_CLIENT_ID'],
    client_secret=app.config['KEYCLOAK_FRONTEND_CLIENT_SECRET'],
    server_metadata_url=app.config['KEYCLOAK_SERVER_METADATA_URL'],
    client_kwargs=app.config['KEYCLOAK_CLIENT_KWARGS'],
)

# ========== 页面路由 ==========
@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/login')
def login():
    """登录"""
    if 'user' in session:
        return redirect(url_for('index'))
    
    redirect_uri = url_for('auth_callback', _external=True)
    return keycloak.authorize_redirect(redirect_uri)

@app.route('/login_page')
def login_page():
    """登录页面"""
    if 'user' in session:
        return redirect(url_for('index'))
    return render_template('login.html')

@app.route('/auth/callback')
def auth_callback():
    """认证回调"""
    try:
        token = keycloak.authorize_access_token()
        user = token.get('userinfo')
        if user:
            session['user'] = user
            session['token'] = token
            session['user_info'] = {
                'id': user.get('sub'),
                'username': user.get('preferred_username'),
                'email': user.get('email'),
                'name': user.get('name'),
                'roles': user.get('realm_access', {}).get('roles', [])
            }
        return redirect(url_for('index'))
    except Exception as e:
        print(f"认证回调错误: {e}")
        return redirect(url_for('login_page') + '?error=登录失败')

# 错误处理
@app.errorhandler(404)
def not_found(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

if __name__ == '__main__':
    print("启动测试应用...")
    print("访问: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
